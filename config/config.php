<?php
// 全局配置文件

// 应用配置
define('APP_NAME', 'XFont');
define('APP_VERSION', '1.0.0');
define('BASE_URL', '/');

// 文件上传配置
define('UPLOAD_DIR', __DIR__ . '/../uploads/');
define('FONTS_DIR', __DIR__ . '/../assets/fonts/');
define('MAX_FILE_SIZE', 50 * 1024 * 1024); // 50MB
define('ALLOWED_EXTENSIONS', ['ttf', 'otf', 'woff', 'woff2']);

// 分页配置
define('FONTS_PER_PAGE', 24);

// 字体分类
define('FONT_CATEGORIES', [
    'serif' => '衬线体',
    'sans-serif' => '无衬线体',
    'handwriting' => '手写体',
    'brush' => '毛笔体',
    'display' => '装饰体',
    'monospace' => '等宽体',
    'other' => '其他'
]);

// 字体权重
define('FONT_WEIGHTS', [
    '100' => 'Thin',
    '200' => 'Extra Light',
    '300' => 'Light',
    '400' => 'Regular',
    '500' => 'Medium',
    '600' => 'Semi Bold',
    '700' => 'Bold',
    '800' => 'Extra Bold',
    '900' => 'Black'
]);

// 错误处理
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../logs/error.log');

// 确保必要的目录存在
$dirs = [
    UPLOAD_DIR,
    FONTS_DIR,
    __DIR__ . '/../database/',
    __DIR__ . '/../logs/'
];

foreach ($dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// 会话配置
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// 设置默认预览文字
if (!isset($_SESSION['preview_text'])) {
    $_SESSION['preview_text'] = 'The quick brown fox jumps over the lazy dog';
}

// 工具函数
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, 2) . ' ' . $units[$pow];
}

function sanitizeFilename($filename) {
    // 移除危险字符
    $filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $filename);
    // 移除多个连续的下划线
    $filename = preg_replace('/_+/', '_', $filename);
    // 移除开头和结尾的下划线
    $filename = trim($filename, '_');
    
    return $filename;
}

function generateUniqueFilename($originalName, $directory) {
    $pathInfo = pathinfo($originalName);
    $baseName = sanitizeFilename($pathInfo['filename']);
    $extension = strtolower($pathInfo['extension']);
    
    $filename = $baseName . '.' . $extension;
    $counter = 1;
    
    while (file_exists($directory . $filename)) {
        $filename = $baseName . '_' . $counter . '.' . $extension;
        $counter++;
    }
    
    return $filename;
}

function jsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

function errorResponse($message, $statusCode = 400) {
    jsonResponse(['error' => $message], $statusCode);
}

function successResponse($data = [], $message = 'Success') {
    jsonResponse(['success' => true, 'message' => $message, 'data' => $data]);
}
