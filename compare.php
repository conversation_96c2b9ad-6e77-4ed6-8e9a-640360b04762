<?php
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/includes/functions.php';

$comparisons = $fontManager->getComparisons();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字体对比 - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/compare.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <a href="index.php" class="logo"><?php echo APP_NAME; ?></a>

            <div class="header-controls">
                <input type="text" id="previewTextInput" class="preview-text-input"
                       placeholder="自定义预览文字..." value="<?php echo htmlspecialchars($_SESSION['preview_text']); ?>">

                <a href="index.php" class="back-btn">← 返回首页</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="page-header">
            <h1>字体对比</h1>
            <div class="compare-controls">
                <span>共 <?php echo count($comparisons); ?> 个字体</span>
                <?php if (!empty($comparisons)): ?>
                <button id="clearAllBtn" class="action-btn">清空全部</button>
                <?php endif; ?>
            </div>
        </div>

        <?php if (empty($comparisons)): ?>
        <div class="empty-state">
            <div class="empty-icon">⚖️</div>
            <h2>还没有添加对比字体</h2>
            <p>在字体卡片上点击"对比"按钮来添加要对比的字体（最多10个）</p>
            <a href="index.php" class="action-btn primary">浏览字体</a>
        </div>
        <?php else: ?>
        <!-- Compare Controls -->
        <div class="compare-controls-section">
            <div class="font-size-control">
                <label for="fontSizeSlider">字体大小:</label>
                <input type="range" id="fontSizeSlider" class="font-size-slider"
                       min="12" max="72" value="32" step="2">
                <span id="fontSizeValue">32px</span>
            </div>

            <div class="layout-control">
                <label>布局:</label>
                <button class="layout-btn active" data-layout="vertical">垂直对比</button>
                <button class="layout-btn" data-layout="horizontal">水平对比</button>
            </div>
        </div>

        <!-- Compare Grid -->
        <div id="compareGrid" class="compare-grid vertical">
            <?php foreach ($comparisons as $font): ?>
            <div class="compare-item" data-font-id="<?php echo $font['id']; ?>">
                <div class="compare-preview" style="font-family: '<?php echo htmlspecialchars($font['family']); ?>'; font-size: 32px;">
                    <?php echo htmlspecialchars($_SESSION['preview_text']); ?>
                </div>
                <div class="compare-info">
                    <div class="font-name"><?php echo htmlspecialchars($font['name']); ?></div>
                    <div class="font-meta">
                        <span><?php echo htmlspecialchars($font['style']); ?> <?php echo htmlspecialchars($font['weight']); ?></span>
                        <span><?php echo FONT_CATEGORIES[$font['category']] ?? $font['category']; ?></span>
                    </div>
                    <div class="compare-actions">
                        <button class="action-btn download-btn" data-font-id="<?php echo $font['id']; ?>">下载</button>
                        <button class="action-btn" onclick="window.location.href='font-detail.php?id=<?php echo $font['id']; ?>'">详情</button>
                        <button class="action-btn remove-btn" data-font-id="<?php echo $font['id']; ?>">移除</button>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </main>

    <script src="assets/js/app.js"></script>
    <script>
        class CompareManager {
            constructor() {
                this.init();
            }

            init() {
                this.bindEvents();
            }

            bindEvents() {
                // 预览文字更新
                document.getElementById('previewTextInput').addEventListener('input', (e) => {
                    this.updatePreviewText(e.target.value);
                });

                // 字体大小调整
                document.getElementById('fontSizeSlider').addEventListener('input', (e) => {
                    this.updateFontSize(e.target.value);
                });

                // 布局切换
                document.querySelectorAll('.layout-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        this.switchLayout(e.target.dataset.layout);
                    });
                });

                // 清空全部
                const clearAllBtn = document.getElementById('clearAllBtn');
                if (clearAllBtn) {
                    clearAllBtn.addEventListener('click', () => {
                        this.clearAll();
                    });
                }

                // 移除单个字体
                document.querySelectorAll('.remove-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const fontId = e.target.dataset.fontId;
                        this.removeFont(fontId);
                    });
                });

                // 下载字体
                document.querySelectorAll('.download-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const fontId = e.target.dataset.fontId;
                        this.downloadFont(fontId);
                    });
                });
            }

            updatePreviewText(text) {
                document.querySelectorAll('.compare-preview').forEach(preview => {
                    preview.textContent = text || preview.dataset.fontName || 'Preview Text';
                });
            }

            updateFontSize(size) {
                document.getElementById('fontSizeValue').textContent = size + 'px';
                document.querySelectorAll('.compare-preview').forEach(preview => {
                    preview.style.fontSize = size + 'px';
                });
            }

            switchLayout(layout) {
                // 更新按钮状态
                document.querySelectorAll('.layout-btn').forEach(btn => {
                    btn.classList.toggle('active', btn.dataset.layout === layout);
                });

                // 更新网格布局
                const compareGrid = document.getElementById('compareGrid');
                compareGrid.className = `compare-grid ${layout}`;
            }

            async clearAll() {
                if (!confirm('确定要清空所有对比字体吗？')) {
                    return;
                }

                try {
                    const response = await fetch('/api/compare.php?action=clear', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' }
                    });

                    const data = await response.json();

                    if (data.success) {
                        location.reload();
                    } else {
                        alert(data.message || '操作失败');
                    }
                } catch (error) {
                    console.error('清空失败:', error);
                    alert('操作失败，请重试');
                }
            }

            async removeFont(fontId) {
                try {
                    const response = await fetch('api/compare.php?action=remove', {
                        method: 'DELETE',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ font_id: fontId })
                    });

                    const data = await response.json();

                    if (data.success) {
                        // 移除DOM元素
                        const item = document.querySelector(`[data-font-id="${fontId}"]`);
                        if (item) {
                            item.remove();
                        }

                        // 检查是否还有字体
                        const remainingItems = document.querySelectorAll('.compare-item');
                        if (remainingItems.length === 0) {
                            location.reload();
                        }
                    } else {
                        alert(data.message || '操作失败');
                    }
                } catch (error) {
                    console.error('移除失败:', error);
                    alert('操作失败，请重试');
                }
            }

            async downloadFont(fontId) {
                try {
                    const response = await fetch(`/api/fonts.php?action=download&id=${fontId}`);

                    if (response.ok) {
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;

                        const contentDisposition = response.headers.get('Content-Disposition');
                        const filename = contentDisposition
                            ? contentDisposition.split('filename=')[1].replace(/"/g, '')
                            : 'font-file';

                        a.download = filename;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        window.URL.revokeObjectURL(url);
                    } else {
                        throw new Error('下载失败');
                    }
                } catch (error) {
                    console.error('下载失败:', error);
                    alert('下载失败，请重试');
                }
            }
        }

        // 初始化对比管理器
        document.addEventListener('DOMContentLoaded', () => {
            new CompareManager();
        });
    </script>
</body>
</html>
