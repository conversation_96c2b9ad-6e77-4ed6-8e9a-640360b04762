<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XFont - 测试页面</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            text-align: center;
        }
        .test-status {
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>XFont 资源加载测试</h1>

        <div id="css-status" class="test-status error">
            CSS 文件: 未加载
        </div>

        <div id="js-status" class="test-status error">
            JavaScript 文件: 未加载
        </div>

        <div class="test-status">
            <p>如果上面显示绿色的"成功"状态，说明资源文件加载正常。</p>
            <p>然后你可以访问 <a href="index.php">index.php</a> 来使用完整的应用。</p>
            <p><strong>注意:</strong> 要使用完整功能，需要运行PHP服务器：</p>
            <code>php -S localhost:8000</code>
        </div>

        <div class="test-status">
            <h3>修复内容:</h3>
            <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                <li>✅ 修复了CSS和JS文件的404错误（改为相对路径）</li>
                <li>✅ 修复了所有导航链接的路径问题</li>
                <li>✅ 修复了API调用的路径问题</li>
                <li>✅ 改进了错误处理，提供更好的错误信息</li>
                <li>✅ 更新了字体详情页面的链接格式</li>
            </ul>
        </div>
    </div>

    <script src="assets/js/app.js"></script>
    <script>
        // 检查CSS是否加载
        function checkCSS() {
            const testElement = document.createElement('div');
            testElement.className = 'header';
            testElement.style.position = 'absolute';
            testElement.style.visibility = 'hidden';
            document.body.appendChild(testElement);

            const computedStyle = window.getComputedStyle(testElement);
            const isLoaded = computedStyle.background.includes('rgb(255, 255, 255)') ||
                           computedStyle.backgroundColor === 'rgb(255, 255, 255)' ||
                           computedStyle.borderBottomWidth === '1px';

            document.body.removeChild(testElement);

            const cssStatus = document.getElementById('css-status');
            if (isLoaded) {
                cssStatus.textContent = 'CSS 文件: 加载成功 ✓';
                cssStatus.className = 'test-status success';
            } else {
                cssStatus.textContent = 'CSS 文件: 加载失败 ✗';
                cssStatus.className = 'test-status error';
            }
        }

        // 检查JS是否加载
        function checkJS() {
            const jsStatus = document.getElementById('js-status');
            if (typeof FontManager !== 'undefined') {
                jsStatus.textContent = 'JavaScript 文件: 加载成功 ✓';
                jsStatus.className = 'test-status success';
            } else {
                jsStatus.textContent = 'JavaScript 文件: 加载失败 ✗';
                jsStatus.className = 'test-status error';
            }
        }

        // 页面加载完成后检查
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                checkCSS();
                checkJS();
            }, 100);
        });
    </script>
</body>
</html>
