class FontManager {
    constructor() {
        this.currentCategory = 'all';
        this.currentPage = 1;
        this.currentView = 'grid';
        this.currentFontSize = 24;
        this.previewText = 'The quick brown fox jumps over the lazy dog';
        this.searchQuery = '';

        // Detect if we're running with PHP server or static files
        this.isPhpAvailable = false;
        this.checkPhpAvailability();

        this.init();
    }

    async checkPhpAvailability() {
        try {
            const response = await fetch('api/fonts.php?action=ping', {
                method: 'GET',
                cache: 'no-cache'
            });
            this.isPhpAvailable = response.ok && response.headers.get('content-type')?.includes('application/json');
        } catch (error) {
            this.isPhpAvailable = false;
        }

        if (!this.isPhpAvailable) {
            this.showPhpWarning();
        }
    }

    showPhpWarning() {
        const warningDiv = document.createElement('div');
        warningDiv.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            text-align: center;
            z-index: 1000;
            border-bottom: 1px solid #f5c6cb;
        `;
        warningDiv.innerHTML = `
            <strong>⚠️ PHP服务器未运行</strong> -
            要使用完整功能，请运行: <code>php -S localhost:8000</code>
            <button onclick="this.parentElement.remove()" style="margin-left: 10px; background: #721c24; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">关闭</button>
        `;
        document.body.insertBefore(warningDiv, document.body.firstChild);
    }

    // Helper method to handle API responses with better error messages
    async handleApiResponse(response) {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('Server returned non-JSON response. Please check if PHP server is running.');
        }

        return await response.json();
    }

    init() {
        this.bindEvents();
        this.loadCategories();
        this.loadFonts();
        this.loadPreviewText();
    }

    bindEvents() {
        // 搜索
        document.getElementById('searchBtn').addEventListener('click', () => this.search());
        document.getElementById('searchInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.search();
        });

        // 预览文字
        document.getElementById('previewTextInput').addEventListener('change', (e) => {
            this.updatePreviewText(e.target.value);
        });

        // 上传
        document.getElementById('uploadBtn').addEventListener('click', () => this.showUploadModal());

        // 视图切换
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.switchView(e.target.dataset.view));
        });

        // 字体大小
        document.getElementById('fontSizeSlider').addEventListener('input', (e) => {
            this.updateFontSize(e.target.value);
        });

        // 模态框关闭
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal(e.target);
            }
        });

        // 文件拖拽上传
        this.setupDragAndDrop();
    }

    async loadCategories() {
        if (!this.isPhpAvailable) {
            // Show demo categories when PHP is not available
            this.renderCategories([
                { key: 'serif', name: '衬线体', count: 0 },
                { key: 'sans-serif', name: '无衬线体', count: 0 },
                { key: 'handwriting', name: '手写体', count: 0 }
            ]);
            return;
        }

        try {
            const response = await fetch('api/fonts.php?action=categories');
            const data = await this.handleApiResponse(response);

            if (data.success) {
                this.renderCategories(data.data.categories);
            }
        } catch (error) {
            console.error('加载分类失败:', error);
            // Fallback to demo categories
            this.renderCategories([
                { key: 'serif', name: '衬线体', count: 0 },
                { key: 'sans-serif', name: '无衬线体', count: 0 }
            ]);
        }
    }

    renderCategories(categories) {
        const navTabs = document.querySelector('.nav-tabs');
        navTabs.innerHTML = '';

        // 添加"全部"选项
        const allTab = document.createElement('li');
        allTab.className = 'nav-tab active';
        allTab.dataset.category = 'all';
        allTab.innerHTML = '全部';
        allTab.addEventListener('click', () => this.switchCategory('all'));
        navTabs.appendChild(allTab);

        // 添加分类选项
        categories.forEach(category => {
            const tab = document.createElement('li');
            tab.className = 'nav-tab';
            tab.dataset.category = category.key;
            tab.innerHTML = `${category.name} <span class="count">(${category.count})</span>`;
            tab.addEventListener('click', () => this.switchCategory(category.key));
            navTabs.appendChild(tab);
        });
    }

    switchCategory(category) {
        this.currentCategory = category;
        this.currentPage = 1;

        // 更新导航状态
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.toggle('active', tab.dataset.category === category);
        });

        this.loadFonts();
    }

    switchView(view) {
        this.currentView = view;

        // 更新按钮状态
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.view === view);
        });

        // 更新网格类名
        const fontGrid = document.getElementById('fontGrid');
        fontGrid.classList.toggle('list-view', view === 'list');

        this.renderFonts();
    }

    updateFontSize(size) {
        this.currentFontSize = size;
        document.getElementById('fontSizeValue').textContent = size + 'px';

        // 更新所有字体预览的大小
        document.querySelectorAll('.font-preview').forEach(preview => {
            preview.style.fontSize = size + 'px';
        });
    }

    async updatePreviewText(text) {
        this.previewText = text;

        try {
            await fetch('api/fonts.php?action=set_preview_text', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ text })
            });

            // 更新所有预览文字
            document.querySelectorAll('.font-preview').forEach(preview => {
                preview.textContent = text || preview.dataset.fontName;
            });
        } catch (error) {
            console.error('更新预览文字失败:', error);
        }
    }

    async loadPreviewText() {
        try {
            const response = await fetch('api/fonts.php?action=get_preview_text');
            const data = await response.json();

            if (data.success) {
                this.previewText = data.data.preview_text;
                document.getElementById('previewTextInput').value = this.previewText;
            }
        } catch (error) {
            console.error('加载预览文字失败:', error);
        }
    }

    async loadFonts() {
        try {
            const params = new URLSearchParams({
                action: 'list',
                category: this.currentCategory,
                page: this.currentPage,
                search: this.searchQuery
            });

            const response = await fetch(`api/fonts.php?${params}`);
            const data = await response.json();

            if (data.success) {
                this.renderFonts(data.data.fonts);
                this.renderPagination(data.data);
            }
        } catch (error) {
            console.error('加载字体失败:', error);
        }
    }

    renderFonts(fonts = []) {
        const fontGrid = document.getElementById('fontGrid');
        fontGrid.innerHTML = '';

        if (fonts.length === 0) {
            fontGrid.innerHTML = '<div class="no-fonts">暂无字体</div>';
            return;
        }

        fonts.forEach(font => {
            const fontCard = this.createFontCard(font);
            fontGrid.appendChild(fontCard);
        });
    }

    createFontCard(font) {
        const card = document.createElement('div');
        card.className = `font-card ${this.currentView === 'list' ? 'list-view' : ''}`;
        card.dataset.fontId = font.id;

        const previewText = this.previewText || font.name;

        card.innerHTML = `
            <div class="font-preview" style="font-family: '${font.family}'; font-size: ${this.currentFontSize}px;" data-font-name="${font.name}">
                ${previewText}
            </div>
            <div class="font-info">
                <div class="font-name">${font.name}</div>
                <div class="font-meta">
                    <span>${font.style} ${font.weight}</span>
                    <span>${this.formatFileSize(font.file_size)}</span>
                </div>
                <div class="font-actions">
                    <button class="action-btn download-btn" data-font-id="${font.id}">下载</button>
                    <button class="action-btn favorite-btn ${font.is_favorite ? 'active' : ''}" data-font-id="${font.id}">
                        ${font.is_favorite ? '已收藏' : '收藏'}
                    </button>
                    <button class="action-btn compare-btn ${font.is_in_comparison ? 'active' : ''}" data-font-id="${font.id}">
                        ${font.is_in_comparison ? '已对比' : '对比'}
                    </button>
                </div>
            </div>
        `;

        // 绑定事件
        this.bindFontCardEvents(card, font);

        return card;
    }

    bindFontCardEvents(card, font) {
        // 点击卡片查看详情
        card.addEventListener('click', (e) => {
            if (!e.target.classList.contains('action-btn')) {
                this.showFontDetail(font.id);
            }
        });

        // 下载
        card.querySelector('.download-btn').addEventListener('click', (e) => {
            e.stopPropagation();
            this.downloadFont(font.id);
        });

        // 收藏
        card.querySelector('.favorite-btn').addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleFavorite(font.id, e.target);
        });

        // 对比
        card.querySelector('.compare-btn').addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleComparison(font.id, e.target);
        });
    }

    showFontDetail(fontId) {
        window.location.href = `font-detail.php?id=${fontId}`;
    }

    async downloadFont(fontId) {
        try {
            const response = await fetch(`api/fonts.php?action=download&id=${fontId}`);

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = response.headers.get('Content-Disposition').split('filename=')[1].replace(/"/g, '');
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
            }
        } catch (error) {
            console.error('下载失败:', error);
            alert('下载失败');
        }
    }

    async toggleFavorite(fontId, button) {
        try {
            const isFavorite = button.classList.contains('active');
            const action = isFavorite ? 'remove' : 'add';
            const method = isFavorite ? 'DELETE' : 'POST';

            const response = await fetch(`api/favorites.php?action=${action}`, {
                method,
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ font_id: fontId })
            });

            const data = await response.json();

            if (data.success) {
                button.classList.toggle('active');
                button.textContent = button.classList.contains('active') ? '已收藏' : '收藏';
            } else {
                alert(data.message || '操作失败');
            }
        } catch (error) {
            console.error('收藏操作失败:', error);
            alert('操作失败');
        }
    }

    async toggleComparison(fontId, button) {
        try {
            const isInComparison = button.classList.contains('active');
            const action = isInComparison ? 'remove' : 'add';
            const method = isInComparison ? 'DELETE' : 'POST';

            const response = await fetch(`api/compare.php?action=${action}`, {
                method,
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ font_id: fontId })
            });

            const data = await response.json();

            if (data.success) {
                button.classList.toggle('active');
                button.textContent = button.classList.contains('active') ? '已对比' : '对比';
            } else {
                alert(data.message || '操作失败');
            }
        } catch (error) {
            console.error('对比操作失败:', error);
            alert('操作失败');
        }
    }

    search() {
        this.searchQuery = document.getElementById('searchInput').value.trim();
        this.currentPage = 1;
        this.loadFonts();
    }

    formatFileSize(bytes) {
        const units = ['B', 'KB', 'MB', 'GB'];
        let size = bytes;
        let unitIndex = 0;

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return Math.round(size * 100) / 100 + ' ' + units[unitIndex];
    }

    renderPagination(data) {
        const pagination = document.getElementById('pagination');
        pagination.innerHTML = '';

        if (data.pages <= 1) return;

        // 上一页
        const prevBtn = document.createElement('button');
        prevBtn.className = 'page-btn';
        prevBtn.textContent = '上一页';
        prevBtn.disabled = data.page === 1;
        prevBtn.addEventListener('click', () => this.goToPage(data.page - 1));
        pagination.appendChild(prevBtn);

        // 页码
        for (let i = 1; i <= data.pages; i++) {
            if (i === 1 || i === data.pages || (i >= data.page - 2 && i <= data.page + 2)) {
                const pageBtn = document.createElement('button');
                pageBtn.className = `page-btn ${i === data.page ? 'active' : ''}`;
                pageBtn.textContent = i;
                pageBtn.addEventListener('click', () => this.goToPage(i));
                pagination.appendChild(pageBtn);
            } else if (i === data.page - 3 || i === data.page + 3) {
                const ellipsis = document.createElement('span');
                ellipsis.textContent = '...';
                pagination.appendChild(ellipsis);
            }
        }

        // 下一页
        const nextBtn = document.createElement('button');
        nextBtn.className = 'page-btn';
        nextBtn.textContent = '下一页';
        nextBtn.disabled = data.page === data.pages;
        nextBtn.addEventListener('click', () => this.goToPage(data.page + 1));
        pagination.appendChild(nextBtn);
    }

    goToPage(page) {
        this.currentPage = page;
        this.loadFonts();
    }

    showUploadModal() {
        document.getElementById('uploadModal').classList.add('show');
    }

    closeModal(modal) {
        modal.classList.remove('show');
    }

    setupDragAndDrop() {
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');

        uploadArea.addEventListener('click', () => fileInput.click());

        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            this.handleFiles(e.dataTransfer.files);
        });

        fileInput.addEventListener('change', (e) => {
            this.handleFiles(e.target.files);
        });
    }

    async handleFiles(files) {
        if (files.length === 0) return;

        const formData = new FormData();
        for (let file of files) {
            formData.append('fonts[]', file);
        }

        try {
            const response = await fetch('api/upload.php', {
                method: 'POST',
                body: formData
            });

            const data = await this.handleApiResponse(response);

            if (data.success) {
                alert(`成功上传 ${data.total_uploaded} 个字体文件`);
                this.closeModal(document.getElementById('uploadModal'));
                this.loadCategories();
                this.loadFonts();
            } else {
                alert(data.message || '上传失败');
            }
        } catch (error) {
            console.error('上传失败:', error);
            if (error.message.includes('non-JSON response')) {
                alert('上传失败: 服务器配置错误，请确保PHP服务器正在运行');
            } else {
                alert(`上传失败: ${error.message}`);
            }
        }
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new FontManager();
});
