/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

/* Header */
.header {
    background: #fff;
    border-bottom: 1px solid #e5e5e5;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.logo {
    font-size: 24px;
    font-weight: 700;
    color: #000;
    text-decoration: none;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

.search-box {
    position: relative;
}

.search-input {
    width: 300px;
    padding: 10px 40px 10px 15px;
    border: 1px solid #ddd;
    border-radius: 25px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s;
}

.search-input:focus {
    border-color: #000;
}

.search-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
}

.preview-text-input {
    width: 250px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 20px;
    font-size: 14px;
    outline: none;
}

.upload-btn {
    background: #000;
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.upload-btn:hover {
    background: #333;
}

/* Navigation */
.nav {
    background: #f8f9fa;
    border-bottom: 1px solid #e5e5e5;
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-tabs {
    display: flex;
    gap: 0;
    list-style: none;
}

.nav-tab {
    padding: 15px 25px;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.2s;
    font-weight: 500;
}

.nav-tab:hover,
.nav-tab.active {
    background: #fff;
    border-bottom-color: #000;
}

.nav-tab .count {
    color: #666;
    font-size: 12px;
    margin-left: 5px;
}

/* Main Content */
.main {
    max-width: 1400px;
    margin: 0 auto;
    padding: 30px 20px;
}

/* View Controls */
.view-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.view-toggle {
    display: flex;
    gap: 10px;
}

.view-btn {
    padding: 8px 15px;
    border: 1px solid #ddd;
    background: #fff;
    cursor: pointer;
    border-radius: 5px;
    transition: all 0.2s;
}

.view-btn.active {
    background: #000;
    color: #fff;
    border-color: #000;
}

.font-size-control {
    display: flex;
    align-items: center;
    gap: 10px;
}

.font-size-slider {
    width: 120px;
}

/* Font Grid */
.font-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.font-grid.list-view {
    grid-template-columns: 1fr;
    gap: 10px;
}

/* Font Card */
.font-card {
    background: #fff;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.2s;
    cursor: pointer;
}

.font-card:hover {
    border-color: #000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.font-preview {
    padding: 30px 20px;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 24px;
    background: #fafafa;
    border-bottom: 1px solid #e5e5e5;
}

.font-info {
    padding: 15px 20px;
}

.font-name {
    font-weight: 600;
    margin-bottom: 5px;
    font-size: 16px;
}

.font-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #666;
    margin-bottom: 10px;
}

.font-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    padding: 6px 12px;
    border: 1px solid #ddd;
    background: #fff;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;
}

.action-btn:hover {
    background: #f5f5f5;
}

.action-btn.active {
    background: #000;
    color: #fff;
    border-color: #000;
}

/* List View Styles */
.font-card.list-view {
    display: flex;
    align-items: center;
    padding: 20px;
}

.font-card.list-view .font-preview {
    flex: 1;
    min-height: auto;
    padding: 0;
    background: none;
    border: none;
    text-align: left;
    font-size: 20px;
}

.font-card.list-view .font-info {
    width: 300px;
    padding: 0 0 0 20px;
    border-left: 1px solid #e5e5e5;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 40px;
}

.page-btn {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: #fff;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s;
}

.page-btn:hover,
.page-btn.active {
    background: #000;
    color: #fff;
    border-color: #000;
}

.page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: #fff;
    border-radius: 8px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
}

/* Upload Modal */
.upload-modal .modal-content {
    width: 600px;
    padding: 30px;
}

.upload-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    margin-bottom: 20px;
    transition: border-color 0.2s;
}

.upload-area.dragover {
    border-color: #000;
    background: #f8f9fa;
}

.file-input {
    display: none;
}

.upload-progress {
    margin-top: 20px;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #e5e5e5;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: #000;
    transition: width 0.3s;
}

/* Font Detail Styles */
.font-detail-container {
    max-width: 1200px;
    margin: 0 auto;
}

.font-preview-section {
    background: #f8f9fa;
    padding: 40px;
    border-radius: 8px;
    margin-bottom: 40px;
}

.preview-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    gap: 20px;
}

.font-preview-area {
    background: #fff;
    padding: 40px;
    border-radius: 8px;
    border: 1px solid #e5e5e5;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.font-preview-text {
    text-align: center;
    line-height: 1.2;
    word-break: break-word;
}

.font-info-section {
    margin-bottom: 40px;
}

.font-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    gap: 20px;
}

.font-title {
    font-size: 32px;
    font-weight: 700;
    margin: 0;
}

.font-actions {
    display: flex;
    gap: 10px;
    flex-shrink: 0;
}

.action-btn.primary {
    background: #000;
    color: #fff;
}

.action-btn.danger {
    background: #dc3545;
    color: #fff;
}

.font-meta-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.meta-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.meta-item label {
    font-weight: 600;
    color: #666;
    font-size: 14px;
}

.meta-item span {
    font-size: 16px;
}

.font-description,
.font-tags {
    margin-bottom: 30px;
}

.font-description h3,
.font-tags h3 {
    margin-bottom: 15px;
    font-size: 18px;
}

.tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tag {
    background: #f0f0f0;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 14px;
    color: #666;
}

.related-fonts-section h2 {
    margin-bottom: 20px;
    font-size: 24px;
}

.related-fonts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

/* Edit Modal */
.edit-modal .modal-content {
    width: 600px;
    padding: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-group textarea {
    resize: vertical;
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 30px;
}

.back-btn {
    color: #666;
    text-decoration: none;
    padding: 8px 16px;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: all 0.2s;
}

.back-btn:hover {
    color: #000;
    border-color: #000;
}

/* Responsive */
@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        height: auto;
        padding: 15px 20px;
        gap: 15px;
    }

    .header-controls {
        width: 100%;
        justify-content: space-between;
    }

    .search-input {
        width: 200px;
    }

    .preview-text-input {
        width: 150px;
    }

    .nav-tabs {
        overflow-x: auto;
        white-space: nowrap;
    }

    .font-grid {
        grid-template-columns: 1fr;
    }

    .view-controls {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .font-header {
        flex-direction: column;
        align-items: stretch;
    }

    .font-actions {
        justify-content: center;
    }

    .preview-controls {
        flex-direction: column;
        gap: 15px;
    }

    .font-meta-grid {
        grid-template-columns: 1fr;
    }

    .related-fonts-grid {
        grid-template-columns: 1fr;
    }
}
