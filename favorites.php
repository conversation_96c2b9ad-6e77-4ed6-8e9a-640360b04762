<?php
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/includes/functions.php';

$favorites = $fontManager->getFavorites();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的收藏 - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <a href="/" class="logo"><?php echo APP_NAME; ?></a>
            
            <div class="header-controls">
                <input type="text" id="previewTextInput" class="preview-text-input" 
                       placeholder="自定义预览文字..." value="<?php echo htmlspecialchars($_SESSION['preview_text']); ?>">
                
                <a href="/" class="back-btn">← 返回首页</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="page-header">
            <h1>我的收藏</h1>
            <p>共 <?php echo count($favorites); ?> 个字体</p>
        </div>

        <?php if (empty($favorites)): ?>
        <div class="empty-state">
            <div class="empty-icon">❤️</div>
            <h2>还没有收藏任何字体</h2>
            <p>在字体卡片上点击"收藏"按钮来添加你喜欢的字体</p>
            <a href="/" class="action-btn primary">浏览字体</a>
        </div>
        <?php else: ?>
        <!-- View Controls -->
        <div class="view-controls">
            <div class="view-toggle">
                <button class="view-btn active" data-view="grid">网格视图</button>
                <button class="view-btn" data-view="list">列表视图</button>
            </div>
            
            <div class="font-size-control">
                <label for="fontSizeSlider">字体大小:</label>
                <input type="range" id="fontSizeSlider" class="font-size-slider" 
                       min="12" max="72" value="24" step="2">
                <span id="fontSizeValue">24px</span>
            </div>
        </div>

        <!-- Font Grid -->
        <div id="fontGrid" class="font-grid">
            <?php foreach ($favorites as $font): ?>
            <div class="font-card" data-font-id="<?php echo $font['id']; ?>">
                <div class="font-preview" style="font-family: '<?php echo htmlspecialchars($font['family']); ?>'; font-size: 24px;" 
                     data-font-name="<?php echo htmlspecialchars($font['name']); ?>">
                    <?php echo htmlspecialchars($_SESSION['preview_text']); ?>
                </div>
                <div class="font-info">
                    <div class="font-name"><?php echo htmlspecialchars($font['name']); ?></div>
                    <div class="font-meta">
                        <span><?php echo htmlspecialchars($font['style']); ?> <?php echo htmlspecialchars($font['weight']); ?></span>
                        <span><?php echo formatFileSize($font['file_size']); ?></span>
                    </div>
                    <div class="font-actions">
                        <button class="action-btn download-btn" data-font-id="<?php echo $font['id']; ?>">下载</button>
                        <button class="action-btn favorite-btn active" data-font-id="<?php echo $font['id']; ?>">已收藏</button>
                        <button class="action-btn compare-btn <?php echo $font['is_in_comparison'] ? 'active' : ''; ?>" 
                                data-font-id="<?php echo $font['id']; ?>">
                            <?php echo $font['is_in_comparison'] ? '已对比' : '对比'; ?>
                        </button>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </main>

    <script src="/assets/js/app.js"></script>
    <script>
        // 初始化收藏页面
        document.addEventListener('DOMContentLoaded', () => {
            const fontManager = new FontManager();
            
            // 重写渲染方法，使用现有的字体数据
            fontManager.renderFonts = () => {
                // 字体已经在服务器端渲染，这里只需要绑定事件
                document.querySelectorAll('.font-card').forEach(card => {
                    const fontId = card.dataset.fontId;
                    fontManager.bindFontCardEvents(card, { id: fontId });
                });
            };
            
            // 点击字体卡片查看详情
            document.querySelectorAll('.font-card').forEach(card => {
                card.addEventListener('click', (e) => {
                    if (!e.target.classList.contains('action-btn')) {
                        const fontId = card.dataset.fontId;
                        window.location.href = `/font/${fontId}`;
                    }
                });
            });
        });
    </script>
</body>
</html>
